Unity.Collections.LowLevel.Unsafe.UnsafeUtility::IsBlittable
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCmp
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::SizeOf
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::Free
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCpy
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCpyStride
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemMove
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemSet
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::Malloc
Unity.Jobs.JobHandle::ScheduleBatchedJobs
Unity.Profiling.LowLevel.Unsafe.ProfilerRecorderHandle::GetDescriptionInternal_Injected
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateMarker
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::BeginSample
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::EndSample
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::GetCategoryDescription_Injected
Unity.Profiling.ProfilerRecorder::GetRunning_Injected
Unity.Profiling.ProfilerRecorder::GetValid_Injected
Unity.Profiling.ProfilerRecorder::GetCount_Injected
Unity.Profiling.ProfilerRecorder::GetLastValue_Injected
Unity.Profiling.ProfilerRecorder::Control_Injected
Unity.Profiling.ProfilerRecorder::Create_Injected
Unity.Profiling.ProfilerRecorder::GetSampleInternal_Injected
UnityEngine.AI.NavMeshAgent::SetDestination_Injected
UnityEngine.AI.NavMeshAgent::get_isOnNavMesh
UnityEngine.AI.NavMeshAgent::Move_Injected
UnityEngine.AI.NavMeshAgent::get_desiredVelocity_Injected
UnityEngine.AI.NavMeshAgent::set_acceleration
UnityEngine.AI.NavMeshAgent::set_angularSpeed
UnityEngine.AI.NavMeshAgent::set_avoidancePriority
UnityEngine.AI.NavMeshAgent::set_height
UnityEngine.AI.NavMeshAgent::set_radius
UnityEngine.AI.NavMeshAgent::set_speed
UnityEngine.AI.NavMeshAgent::set_updatePosition
UnityEngine.AndroidJNI::CallBooleanMethod
UnityEngine.AndroidJNI::CallStaticBooleanMethod
UnityEngine.AndroidJNI::GetBooleanArrayElement
UnityEngine.AndroidJNI::GetBooleanField
UnityEngine.AndroidJNI::GetStaticBooleanField
UnityEngine.AndroidJNI::IsAssignableFrom
UnityEngine.AndroidJNI::IsInstanceOf
UnityEngine.AndroidJNI::IsSameObject
UnityEngine.AndroidJNI::FromBooleanArray
UnityEngine.AndroidJNI::FromByteArray
UnityEngine.AndroidJNI::CallCharMethod
UnityEngine.AndroidJNI::CallStaticCharMethod
UnityEngine.AndroidJNI::GetCharArrayElement
UnityEngine.AndroidJNI::GetCharField
UnityEngine.AndroidJNI::GetStaticCharField
UnityEngine.AndroidJNI::FromCharArray
UnityEngine.AndroidJNI::CallDoubleMethod
UnityEngine.AndroidJNI::CallStaticDoubleMethod
UnityEngine.AndroidJNI::GetDoubleArrayElement
UnityEngine.AndroidJNI::GetDoubleField
UnityEngine.AndroidJNI::GetStaticDoubleField
UnityEngine.AndroidJNI::FromDoubleArray
UnityEngine.AndroidJNI::CallShortMethod
UnityEngine.AndroidJNI::CallStaticShortMethod
UnityEngine.AndroidJNI::GetShortArrayElement
UnityEngine.AndroidJNI::GetShortField
UnityEngine.AndroidJNI::GetStaticShortField
UnityEngine.AndroidJNI::FromShortArray
UnityEngine.AndroidJNI::AttachCurrentThread
UnityEngine.AndroidJNI::CallIntMethod
UnityEngine.AndroidJNI::CallStaticIntMethod
UnityEngine.AndroidJNI::DetachCurrentThread
UnityEngine.AndroidJNI::EnsureLocalCapacity
UnityEngine.AndroidJNI::GetArrayLength
UnityEngine.AndroidJNI::GetIntArrayElement
UnityEngine.AndroidJNI::GetIntField
UnityEngine.AndroidJNI::GetStaticIntField
UnityEngine.AndroidJNI::GetStringLength
UnityEngine.AndroidJNI::GetStringUTFLength
UnityEngine.AndroidJNI::GetVersion
UnityEngine.AndroidJNI::PushLocalFrame
UnityEngine.AndroidJNI::Throw
UnityEngine.AndroidJNI::ThrowNew
UnityEngine.AndroidJNI::FromIntArray
UnityEngine.AndroidJNI::CallLongMethod
UnityEngine.AndroidJNI::CallStaticLongMethod
UnityEngine.AndroidJNI::GetLongArrayElement
UnityEngine.AndroidJNI::GetLongField
UnityEngine.AndroidJNI::GetStaticLongField
UnityEngine.AndroidJNI::FromLongArray
UnityEngine.AndroidJNI::AllocObject
UnityEngine.AndroidJNI::CallObjectMethod
UnityEngine.AndroidJNI::CallStaticObjectMethod
UnityEngine.AndroidJNI::ExceptionOccurred
UnityEngine.AndroidJNI::FindClass
UnityEngine.AndroidJNI::FromReflectedField
UnityEngine.AndroidJNI::FromReflectedMethod
UnityEngine.AndroidJNI::GetFieldID
UnityEngine.AndroidJNI::GetMethodID
UnityEngine.AndroidJNI::GetObjectArrayElement
UnityEngine.AndroidJNI::GetObjectClass
UnityEngine.AndroidJNI::GetObjectField
UnityEngine.AndroidJNI::GetStaticFieldID
UnityEngine.AndroidJNI::GetStaticMethodID
UnityEngine.AndroidJNI::GetStaticObjectField
UnityEngine.AndroidJNI::GetSuperclass
UnityEngine.AndroidJNI::NewBooleanArray
UnityEngine.AndroidJNI::NewCharArray
UnityEngine.AndroidJNI::NewDoubleArray
UnityEngine.AndroidJNI::NewFloatArray
UnityEngine.AndroidJNI::NewGlobalRef
UnityEngine.AndroidJNI::NewIntArray
UnityEngine.AndroidJNI::NewLocalRef
UnityEngine.AndroidJNI::NewLongArray
UnityEngine.AndroidJNI::NewObject
UnityEngine.AndroidJNI::NewObjectArray
UnityEngine.AndroidJNI::NewSByteArray
UnityEngine.AndroidJNI::NewShortArray
UnityEngine.AndroidJNI::NewString
UnityEngine.AndroidJNI::NewStringFromStr
UnityEngine.AndroidJNI::NewStringUTF
UnityEngine.AndroidJNI::NewWeakGlobalRef
UnityEngine.AndroidJNI::PopLocalFrame
UnityEngine.AndroidJNI::ToBooleanArray
UnityEngine.AndroidJNI::ToByteArray
UnityEngine.AndroidJNI::ToCharArray
UnityEngine.AndroidJNI::ToDoubleArray
UnityEngine.AndroidJNI::ToFloatArray
UnityEngine.AndroidJNI::ToIntArray
UnityEngine.AndroidJNI::ToLongArray
UnityEngine.AndroidJNI::ToObjectArray
UnityEngine.AndroidJNI::ToReflectedField
UnityEngine.AndroidJNI::ToReflectedMethod
UnityEngine.AndroidJNI::ToSByteArray
UnityEngine.AndroidJNI::ToShortArray
UnityEngine.AndroidJNI::FromObjectArray
UnityEngine.AndroidJNI::CallSByteMethod
UnityEngine.AndroidJNI::CallStaticSByteMethod
UnityEngine.AndroidJNI::GetSByteArrayElement
UnityEngine.AndroidJNI::GetSByteField
UnityEngine.AndroidJNI::GetStaticSByteField
UnityEngine.AndroidJNI::FromSByteArray
UnityEngine.AndroidJNI::CallFloatMethod
UnityEngine.AndroidJNI::CallStaticFloatMethod
UnityEngine.AndroidJNI::GetFloatArrayElement
UnityEngine.AndroidJNI::GetFloatField
UnityEngine.AndroidJNI::GetStaticFloatField
UnityEngine.AndroidJNI::FromFloatArray
UnityEngine.AndroidJNI::CallStaticStringMethod
UnityEngine.AndroidJNI::CallStringMethod
UnityEngine.AndroidJNI::GetStaticStringField
UnityEngine.AndroidJNI::GetStringChars
UnityEngine.AndroidJNI::GetStringField
UnityEngine.AndroidJNI::GetStringUTFChars
UnityEngine.AndroidJNI::CallStaticVoidMethod
UnityEngine.AndroidJNI::CallVoidMethod
UnityEngine.AndroidJNI::DeleteGlobalRef
UnityEngine.AndroidJNI::DeleteLocalRef
UnityEngine.AndroidJNI::DeleteWeakGlobalRef
UnityEngine.AndroidJNI::ExceptionClear
UnityEngine.AndroidJNI::ExceptionDescribe
UnityEngine.AndroidJNI::FatalError
UnityEngine.AndroidJNI::SetBooleanArrayElement
UnityEngine.AndroidJNI::SetBooleanField
UnityEngine.AndroidJNI::SetCharArrayElement
UnityEngine.AndroidJNI::SetCharField
UnityEngine.AndroidJNI::SetDoubleArrayElement
UnityEngine.AndroidJNI::SetDoubleField
UnityEngine.AndroidJNI::SetFloatArrayElement
UnityEngine.AndroidJNI::SetFloatField
UnityEngine.AndroidJNI::SetIntArrayElement
UnityEngine.AndroidJNI::SetIntField
UnityEngine.AndroidJNI::SetLongArrayElement
UnityEngine.AndroidJNI::SetLongField
UnityEngine.AndroidJNI::SetObjectArrayElement
UnityEngine.AndroidJNI::SetObjectField
UnityEngine.AndroidJNI::SetSByteArrayElement
UnityEngine.AndroidJNI::SetSByteField
UnityEngine.AndroidJNI::SetShortArrayElement
UnityEngine.AndroidJNI::SetShortField
UnityEngine.AndroidJNI::SetStaticBooleanField
UnityEngine.AndroidJNI::SetStaticCharField
UnityEngine.AndroidJNI::SetStaticDoubleField
UnityEngine.AndroidJNI::SetStaticFloatField
UnityEngine.AndroidJNI::SetStaticIntField
UnityEngine.AndroidJNI::SetStaticLongField
UnityEngine.AndroidJNI::SetStaticObjectField
UnityEngine.AndroidJNI::SetStaticSByteField
UnityEngine.AndroidJNI::SetStaticShortField
UnityEngine.AndroidJNI::SetStaticStringField
UnityEngine.AndroidJNI::SetStringField
UnityEngine.AndroidJNIHelper::get_debug
UnityEngine.AndroidJNIHelper::set_debug
UnityEngine.Animation::PlayDefaultAnimation
UnityEngine.Animation::GetStateCount
UnityEngine.Animation::Stop
UnityEngine.Animation::GetStateAtIndex
UnityEngine.AnimationClip::get_empty
UnityEngine.AnimationClip::get_hasGenericRootTransform
UnityEngine.AnimationClip::get_hasMotionCurves
UnityEngine.AnimationClip::get_hasRootCurves
UnityEngine.AnimationClip::get_hasRootMotion
UnityEngine.AnimationClip::get_legacy
UnityEngine.AnimationClip::get_frameRate
UnityEngine.AnimationClip::get_length
UnityEngine.AnimationClip::Internal_CreateAnimationClip
UnityEngine.AnimationClip::set_frameRate
UnityEngine.AnimationClip::set_legacy
UnityEngine.AnimationCurve::Internal_Equals
UnityEngine.AnimationCurve::AddKey
UnityEngine.AnimationCurve::AddKey_Internal_Injected
UnityEngine.AnimationCurve::MoveKey_Injected
UnityEngine.AnimationCurve::get_length
UnityEngine.AnimationCurve::Internal_Create
UnityEngine.AnimationCurve::Evaluate
UnityEngine.AnimationCurve::GetKey_Injected
UnityEngine.AnimationCurve::Internal_Destroy
UnityEngine.AnimationCurve::RemoveKey
UnityEngine.AnimationCurve::SetKeys
UnityEngine.AnimationCurve::SmoothTangents
UnityEngine.AnimationCurve::GetKeys
UnityEngine.Animations.AnimationClipPlayable::CreateHandleInternal_Injected
UnityEngine.Animations.AnimationClipPlayable::SetApplyFootIKInternal
UnityEngine.Animations.AnimationClipPlayable::SetLoopTimeInternal
UnityEngine.Animations.AnimationClipPlayable::SetOverrideLoopTimeInternal
UnityEngine.Animations.AnimationClipPlayable::SetRemoveStartOffsetInternal
UnityEngine.Animations.AnimationLayerMixerPlayable::CreateHandleInternal_Injected
UnityEngine.Animations.AnimationLayerMixerPlayable::SetLayerMaskFromAvatarMaskInternal
UnityEngine.Animations.AnimationLayerMixerPlayable::SetSingleLayerOptimizationInternal
UnityEngine.Animations.AnimationMixerPlayable::CreateHandleInternal_Injected
UnityEngine.Animations.AnimationMotionXToDeltaPlayable::CreateHandleInternal_Injected
UnityEngine.Animations.AnimationMotionXToDeltaPlayable::SetAbsoluteMotionInternal
UnityEngine.Animations.AnimationOffsetPlayable::CreateHandleInternal_Injected
UnityEngine.Animations.AnimationPlayableExtensions::SetAnimatedPropertiesInternal
UnityEngine.Animations.AnimationPlayableGraphExtensions::InternalCreateAnimationOutput
UnityEngine.Animations.AnimationPlayableOutput::InternalSetTarget
UnityEngine.Animations.AnimationPlayableOutput::InternalGetTarget
UnityEngine.Animations.AnimationRemoveScalePlayable::CreateHandleInternal_Injected
UnityEngine.Animator::IsInTransition
UnityEngine.Animator::get_hasBoundPlayables
UnityEngine.Animator::get_hasRootMotion
UnityEngine.Animator::get_isHuman
UnityEngine.Animator::StringToHash
UnityEngine.Animator::get_layerCount
UnityEngine.Animator::GetAnimatorClipInfoInternal
UnityEngine.Animator::GetAnimatorStateInfo
UnityEngine.Animator::ResetTriggerString
UnityEngine.Animator::SetBoolString
UnityEngine.Animator::SetFloatString
UnityEngine.Animator::SetTriggerString
UnityEngine.Animator::get_avatar
UnityEngine.Animator::get_runtimeAnimatorController
UnityEngine.AnimatorClipInfo::InstanceIDToAnimationClipPPtr
UnityEngine.Application::get_isBatchMode
UnityEngine.Application::get_isFocused
UnityEngine.Application::get_isPlaying
UnityEngine.Application::get_runInBackground
UnityEngine.Application::get_streamingAssetsPath
UnityEngine.Application::get_unityVersion
UnityEngine.Application::OpenURL
UnityEngine.Application::Quit
UnityEngine.Application::set_targetFrameRate
UnityEngine.Application::get_platform
UnityEngine.Application::GetStackTraceLogType
UnityEngine.AsyncOperation::get_isDone
UnityEngine.AsyncOperation::get_progress
UnityEngine.AsyncOperation::InternalDestroy
UnityEngine.Audio.AudioClipPlayable::InternalCreateAudioClipPlayable
UnityEngine.Audio.AudioClipPlayable::SetPauseDelayInternal
UnityEngine.Audio.AudioClipPlayable::SetSpatialBlendInternal
UnityEngine.Audio.AudioClipPlayable::SetStartDelayInternal
UnityEngine.Audio.AudioClipPlayable::SetStereoPanInternal
UnityEngine.Audio.AudioClipPlayable::SetVolumeInternal
UnityEngine.Audio.AudioMixer::GetFloat
UnityEngine.Audio.AudioMixer::SetFloat
UnityEngine.Audio.AudioMixerPlayable::CreateAudioMixerPlayableInternal
UnityEngine.Audio.AudioPlayableGraphExtensions::InternalCreateAudioOutput
UnityEngine.Audio.AudioPlayableOutput::InternalSetEvaluateOnSeek
UnityEngine.Audio.AudioPlayableOutput::InternalSetTarget
UnityEngine.AudioClip::get_frequency
UnityEngine.AudioClip::get_samples
UnityEngine.AudioClip::get_length
UnityEngine.AudioHighPassFilter::set_cutoffFrequency
UnityEngine.AudioHighPassFilter::set_highpassResonanceQ
UnityEngine.AudioListener::set_volume
UnityEngine.AudioLowPassFilter::set_cutoffFrequency
UnityEngine.AudioSettings::StartAudioOutput
UnityEngine.AudioSettings::StopAudioOutput
UnityEngine.AudioSource::get_isPlaying
UnityEngine.AudioSource::GetPitch
UnityEngine.AudioSource::get_volume
UnityEngine.AudioSource::Pause
UnityEngine.AudioSource::Play
UnityEngine.AudioSource::PlayHelper
UnityEngine.AudioSource::PlayOneShotHelper
UnityEngine.AudioSource::SetPitch
UnityEngine.AudioSource::Stop
UnityEngine.AudioSource::set_clip
UnityEngine.AudioSource::set_dopplerLevel
UnityEngine.AudioSource::set_ignoreListenerPause
UnityEngine.AudioSource::set_ignoreListenerVolume
UnityEngine.AudioSource::set_loop
UnityEngine.AudioSource::set_maxDistance
UnityEngine.AudioSource::set_minDistance
UnityEngine.AudioSource::set_outputAudioMixerGroup
UnityEngine.AudioSource::set_playOnAwake
UnityEngine.AudioSource::set_spatialBlend
UnityEngine.AudioSource::set_volume
UnityEngine.AudioSource::get_clip
UnityEngine.Avatar::get_humanDescription_Injected
UnityEngine.AvatarMask::GetHumanoidBodyPartActive
UnityEngine.AvatarMask::get_transformCount
UnityEngine.AvatarMask::GetTransformWeight
UnityEngine.AvatarMask::GetTransformPath
UnityEngine.Behaviour::get_enabled
UnityEngine.Behaviour::get_isActiveAndEnabled
UnityEngine.Behaviour::set_enabled
UnityEngine.BoxCollider::get_size_Injected
UnityEngine.BoxCollider::set_center_Injected
UnityEngine.BoxCollider::set_size_Injected
UnityEngine.Camera::get_orthographic
UnityEngine.Camera::get_stereoEnabled
UnityEngine.Camera::get_usePhysicalProperties
UnityEngine.Camera::GetAllCamerasCount
UnityEngine.Camera::GetAllCamerasImpl
UnityEngine.Camera::get_cullingMask
UnityEngine.Camera::get_eventMask
UnityEngine.Camera::get_pixelHeight
UnityEngine.Camera::get_pixelWidth
UnityEngine.Camera::get_targetDisplay
UnityEngine.Camera::FieldOfViewToFocalLength
UnityEngine.Camera::get_aspect
UnityEngine.Camera::get_depth
UnityEngine.Camera::get_farClipPlane
UnityEngine.Camera::get_fieldOfView
UnityEngine.Camera::get_nearClipPlane
UnityEngine.Camera::get_orthographicSize
UnityEngine.Camera::ResetProjectionMatrix
UnityEngine.Camera::ResetWorldToCameraMatrix
UnityEngine.Camera::ScreenPointToRay_Injected
UnityEngine.Camera::ScreenToViewportPoint_Injected
UnityEngine.Camera::SetupCurrent
UnityEngine.Camera::WorldToScreenPoint_Injected
UnityEngine.Camera::get_backgroundColor_Injected
UnityEngine.Camera::get_cameraToWorldMatrix_Injected
UnityEngine.Camera::get_lensShift_Injected
UnityEngine.Camera::get_pixelRect_Injected
UnityEngine.Camera::get_projectionMatrix_Injected
UnityEngine.Camera::get_rect_Injected
UnityEngine.Camera::get_sensorSize_Injected
UnityEngine.Camera::get_worldToCameraMatrix_Injected
UnityEngine.Camera::set_aspect
UnityEngine.Camera::set_backgroundColor_Injected
UnityEngine.Camera::set_depth
UnityEngine.Camera::set_farClipPlane
UnityEngine.Camera::set_fieldOfView
UnityEngine.Camera::set_focalLength
UnityEngine.Camera::set_gateFit
UnityEngine.Camera::set_lensShift_Injected
UnityEngine.Camera::set_nearClipPlane
UnityEngine.Camera::set_orthographic
UnityEngine.Camera::set_orthographicSize
UnityEngine.Camera::set_pixelRect_Injected
UnityEngine.Camera::set_projectionMatrix_Injected
UnityEngine.Camera::set_rect_Injected
UnityEngine.Camera::set_sensorSize_Injected
UnityEngine.Camera::set_useOcclusionCulling
UnityEngine.Camera::set_usePhysicalProperties
UnityEngine.Camera::get_current
UnityEngine.Camera::get_main
UnityEngine.Camera::get_gateFit
UnityEngine.Camera::get_clearFlags
UnityEngine.Camera::get_cameraType
UnityEngine.Camera::get_targetTexture
UnityEngine.CameraRaycastHelper::RaycastTry2D_Injected
UnityEngine.CameraRaycastHelper::RaycastTry_Injected
UnityEngine.Canvas::get_isRootCanvas
UnityEngine.Canvas::get_overrideSorting
UnityEngine.Canvas::get_pixelPerfect
UnityEngine.Canvas::get_renderOrder
UnityEngine.Canvas::get_sortingLayerID
UnityEngine.Canvas::get_sortingOrder
UnityEngine.Canvas::get_targetDisplay
UnityEngine.Canvas::get_referencePixelsPerUnit
UnityEngine.Canvas::get_scaleFactor
UnityEngine.Canvas::SetExternalCanvasEnabled
UnityEngine.Canvas::get_pixelRect_Injected
UnityEngine.Canvas::get_renderingDisplaySize_Injected
UnityEngine.Canvas::set_additionalShaderChannels
UnityEngine.Canvas::set_overrideSorting
UnityEngine.Canvas::set_planeDistance
UnityEngine.Canvas::set_referencePixelsPerUnit
UnityEngine.Canvas::set_renderMode
UnityEngine.Canvas::set_scaleFactor
UnityEngine.Canvas::set_sortingLayerID
UnityEngine.Canvas::set_sortingOrder
UnityEngine.Canvas::set_worldCamera
UnityEngine.Canvas::get_additionalShaderChannels
UnityEngine.Canvas::get_worldCamera
UnityEngine.Canvas::get_rootCanvas
UnityEngine.Canvas::GetDefaultCanvasMaterial
UnityEngine.Canvas::GetETC1SupportedCanvasMaterial
UnityEngine.Canvas::get_renderMode
UnityEngine.CanvasGroup::get_blocksRaycasts
UnityEngine.CanvasGroup::get_ignoreParentGroups
UnityEngine.CanvasGroup::get_interactable
UnityEngine.CanvasGroup::get_alpha
UnityEngine.CanvasGroup::set_alpha
UnityEngine.CanvasGroup::set_ignoreParentGroups
UnityEngine.CanvasRenderer::get_cull
UnityEngine.CanvasRenderer::get_cullTransparentMesh
UnityEngine.CanvasRenderer::get_hasMoved
UnityEngine.CanvasRenderer::get_absoluteDepth
UnityEngine.CanvasRenderer::get_materialCount
UnityEngine.CanvasRenderer::Clear
UnityEngine.CanvasRenderer::CreateUIVertexStreamInternal
UnityEngine.CanvasRenderer::DisableRectClipping
UnityEngine.CanvasRenderer::EnableRectClipping_Injected
UnityEngine.CanvasRenderer::GetColor_Injected
UnityEngine.CanvasRenderer::SetAlphaTexture
UnityEngine.CanvasRenderer::SetColor_Injected
UnityEngine.CanvasRenderer::SetMaterial
UnityEngine.CanvasRenderer::SetMesh
UnityEngine.CanvasRenderer::SetPopMaterial
UnityEngine.CanvasRenderer::SetTexture
UnityEngine.CanvasRenderer::SplitIndicesStreamsInternal
UnityEngine.CanvasRenderer::SplitUIVertexStreamsInternal
UnityEngine.CanvasRenderer::set_clippingSoftness_Injected
UnityEngine.CanvasRenderer::set_cull
UnityEngine.CanvasRenderer::set_cullTransparentMesh
UnityEngine.CanvasRenderer::set_hasPopInstruction
UnityEngine.CanvasRenderer::set_materialCount
UnityEngine.CanvasRenderer::set_popMaterialCount
UnityEngine.CanvasRenderer::GetMaterial
UnityEngine.CapsuleCollider::set_center_Injected
UnityEngine.CapsuleCollider::set_height
UnityEngine.CapsuleCollider::set_radius
UnityEngine.Collider::get_enabled
UnityEngine.Collider::get_isTrigger
UnityEngine.Collider::ClosestPoint_Injected
UnityEngine.Collider::Internal_ClosestPointOnBounds_Injected
UnityEngine.Collider::Raycast_Injected
UnityEngine.Collider::get_bounds_Injected
UnityEngine.Collider::set_enabled
UnityEngine.Collider::set_isTrigger
UnityEngine.Collider::get_sharedMaterial
UnityEngine.Collider::get_attachedRigidbody
UnityEngine.Collider2D::OverlapPoint_Injected
UnityEngine.Collider2D::get_offset_Injected
UnityEngine.Collider2D::get_attachedRigidbody
UnityEngine.Component::BroadcastMessage
UnityEngine.Component::GetComponentFastPath
UnityEngine.Component::GetComponentsForListInternal
UnityEngine.Component::SendMessage
UnityEngine.Component::get_gameObject
UnityEngine.Component::get_transform
UnityEngine.CompositeCollider2D::GetPathArray_Internal
UnityEngine.CompositeCollider2D::get_pathCount
UnityEngine.CompositeCollider2D::get_pointCount
UnityEngine.ComputeBuffer::get_count
UnityEngine.ComputeBuffer::get_stride
UnityEngine.ComputeBuffer::InitBuffer
UnityEngine.ComputeBuffer::DestroyBuffer
UnityEngine.ComputeBuffer::InternalSetData
UnityEngine.ComputeBuffer::SetName
UnityEngine.ComputeShader::FindKernel
UnityEngine.ComputeShader::DisableKeyword
UnityEngine.ComputeShader::EnableKeyword
UnityEngine.ComputeShader::SetConstantComputeBuffer
UnityEngine.ConfigurableJoint::get_angularYLimit_Injected
UnityEngine.ConfigurableJoint::get_angularZLimit_Injected
UnityEngine.ConfigurableJoint::get_highAngularXLimit_Injected
UnityEngine.ConfigurableJoint::get_linearLimit_Injected
UnityEngine.ConfigurableJoint::get_lowAngularXLimit_Injected
UnityEngine.ConfigurableJoint::set_angularXMotion
UnityEngine.ConfigurableJoint::set_angularYLimit_Injected
UnityEngine.ConfigurableJoint::set_angularYMotion
UnityEngine.ConfigurableJoint::set_angularZLimit_Injected
UnityEngine.ConfigurableJoint::set_angularZMotion
UnityEngine.ConfigurableJoint::set_configuredInWorldSpace
UnityEngine.ConfigurableJoint::set_highAngularXLimit_Injected
UnityEngine.ConfigurableJoint::set_linearLimit_Injected
UnityEngine.ConfigurableJoint::set_lowAngularXLimit_Injected
UnityEngine.ConfigurableJoint::set_xMotion
UnityEngine.ConfigurableJoint::set_yMotion
UnityEngine.ConfigurableJoint::set_zMotion
UnityEngine.ConfigurableJoint::get_angularXMotion
UnityEngine.ConfigurableJoint::get_angularYMotion
UnityEngine.ConfigurableJoint::get_angularZMotion
UnityEngine.ConfigurableJoint::get_xMotion
UnityEngine.ConfigurableJoint::get_yMotion
UnityEngine.ConfigurableJoint::get_zMotion
UnityEngine.ContactFilter2D::CheckConsistency_Injected
UnityEngine.Coroutine::ReleaseCoroutine
UnityEngine.Cubemap::Internal_CreateImpl
UnityEngine.Cubemap::get_isReadable
UnityEngine.Cubemap::ApplyImpl
UnityEngine.Cubemap::SetPixelImpl_Injected
UnityEngine.CubemapArray::Internal_CreateImpl
UnityEngine.CubemapArray::get_isReadable
UnityEngine.CubemapArray::ApplyImpl
UnityEngine.CubemapArray::SetPixels
UnityEngine.Cursor::SetCursor_Injected
UnityEngine.Cursor::set_lockState
UnityEngine.Cursor::get_lockState
UnityEngine.Debug::ExtractStackTraceNoAlloc
UnityEngine.Debug::DrawLine_Injected
UnityEngine.DebugLogHandler::Internal_Log
UnityEngine.DebugLogHandler::Internal_LogException
UnityEngine.Display::RelativeMouseAtImpl
UnityEngine.Display::GetRenderingExtImpl
UnityEngine.Display::GetSystemExtImpl
UnityEngine.DynamicGI::UpdateEnvironment
UnityEngine.Event::PopEvent
UnityEngine.Event::get_character
UnityEngine.Event::GetDoubleClickTime
UnityEngine.Event::get_button
UnityEngine.Event::get_clickCount
UnityEngine.Event::get_displayIndex
UnityEngine.Event::Internal_Create
UnityEngine.Event::get_pressure
UnityEngine.Event::get_commandName
UnityEngine.Event::CopyFromPtr
UnityEngine.Event::Internal_Destroy
UnityEngine.Event::Internal_SetNativeEvent
UnityEngine.Event::Internal_Use
UnityEngine.Event::get_delta_Injected
UnityEngine.Event::get_mousePosition_Injected
UnityEngine.Event::set_character
UnityEngine.Event::set_commandName
UnityEngine.Event::set_delta_Injected
UnityEngine.Event::set_displayIndex
UnityEngine.Event::set_keyCode
UnityEngine.Event::set_modifiers
UnityEngine.Event::set_mousePosition_Injected
UnityEngine.Event::set_type
UnityEngine.Event::get_modifiers
UnityEngine.Event::get_rawType
UnityEngine.Event::get_type
UnityEngine.Event::get_keyCode
UnityEngine.Event::get_pointerType
UnityEngine.Experimental.Rendering.BuiltinRuntimeReflectionSystem::BuiltinUpdate
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::CanDecompressFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsAlphaOnlyFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsCompressedTextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsDepthFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsPVRTCFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsSRGBFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsStencilFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetDepthBits
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetBlockSize
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetComponentCount
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetDepthStencilFormatFromBitsLegacy_Native
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_RenderTextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_TextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetRenderTextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetSwizzleA
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetSwizzleB
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetSwizzleG
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetSwizzleR
UnityEngine.Experimental.Rendering.ScriptableRuntimeReflectionSystemSettings::ScriptingDirtyReflectionSystemInstance
UnityEngine.Flare::Internal_Create
UnityEngine.Font::HasCharacter
UnityEngine.Font::get_dynamic
UnityEngine.Font::get_fontSize
UnityEngine.Font::Internal_CreateFont
UnityEngine.Font::get_material
UnityEngine.GameObject::GetComponentsInternal
UnityEngine.GameObject::CompareTag
UnityEngine.GameObject::get_activeInHierarchy
UnityEngine.GameObject::get_activeSelf
UnityEngine.GameObject::get_layer
UnityEngine.GameObject::get_tag
UnityEngine.GameObject::GetComponentFastPath
UnityEngine.GameObject::Internal_CreateGameObject
UnityEngine.GameObject::SendMessage
UnityEngine.GameObject::SetActive
UnityEngine.GameObject::TryGetComponentFastPath
UnityEngine.GameObject::set_layer
UnityEngine.GameObject::set_tag
UnityEngine.GameObject::GetComponent
UnityEngine.GameObject::GetComponentInChildren
UnityEngine.GameObject::GetComponentInParent
UnityEngine.GameObject::Internal_AddComponentWithType
UnityEngine.GameObject::Find
UnityEngine.GameObject::FindGameObjectsWithTag
UnityEngine.GameObject::get_transform
UnityEngine.GeometryUtility::TestPlanesAABB_Injected
UnityEngine.GeometryUtility::Internal_ExtractPlanes_Injected
UnityEngine.Gizmos::DrawCube_Injected
UnityEngine.Gizmos::DrawIcon_Injected
UnityEngine.Gizmos::DrawLine_Injected
UnityEngine.Gizmos::DrawSphere_Injected
UnityEngine.Gizmos::DrawWireCube_Injected
UnityEngine.Gizmos::DrawWireSphere_Injected
UnityEngine.Gizmos::set_color_Injected
UnityEngine.Gizmos::set_matrix_Injected
UnityEngine.GL::Begin
UnityEngine.GL::End
UnityEngine.GL::GLClear_Injected
UnityEngine.GL::GLLoadPixelMatrixScript
UnityEngine.GL::ImmediateColor
UnityEngine.GL::LoadOrtho
UnityEngine.GL::LoadProjectionMatrix_Injected
UnityEngine.GL::PopMatrix
UnityEngine.GL::PushMatrix
UnityEngine.GL::SetViewMatrix_Injected
UnityEngine.GL::TexCoord3
UnityEngine.GL::Vertex3
UnityEngine.GL::Viewport_Injected
UnityEngine.GL::set_invertCulling
UnityEngine.Gradient::Internal_Equals
UnityEngine.Gradient::Init
UnityEngine.Gradient::Cleanup
UnityEngine.Gradient::Evaluate_Injected
UnityEngine.Gradient::SetKeys
UnityEngine.Gradient::get_colorKeys
UnityEngine.Graphics::Internal_GetMaxDrawMeshInstanceCount
UnityEngine.Graphics::CopyTexture_Region
UnityEngine.Graphics::CopyTexture_Slice
UnityEngine.Graphics::ExecuteCommandBuffer
UnityEngine.Graphics::Internal_DrawMeshInstanced
UnityEngine.Graphics::Internal_SetNullRT
UnityEngine.Graphics::Internal_SetRTSimple_Injected
UnityEngine.GUI::HasMouseControl
UnityEngine.GUI::get_changed
UnityEngine.GUI::get_enabled
UnityEngine.GUI::GrabMouseControl
UnityEngine.GUI::ReleaseMouseControl
UnityEngine.GUI::get_backgroundColor_Injected
UnityEngine.GUI::get_color_Injected
UnityEngine.GUI::get_contentColor_Injected
UnityEngine.GUI::set_backgroundColor_Injected
UnityEngine.GUI::set_changed
UnityEngine.GUI::set_color_Injected
UnityEngine.GUI::set_contentColor_Injected
UnityEngine.GUI::set_enabled
UnityEngine.GUIClip::Internal_GetCount
UnityEngine.GUIClip::GetMatrix_Injected
UnityEngine.GUIClip::Internal_Pop
UnityEngine.GUIClip::Internal_PopParentClip
UnityEngine.GUIClip::Internal_PushParentClip_Injected
UnityEngine.GUIClip::Internal_Push_Injected
UnityEngine.GUIClip::SetMatrix_Injected
UnityEngine.GUIClip::get_visibleRect_Injected
UnityEngine.GUILayoutUtility::Internal_GetWindowRect_Injected
UnityEngine.GUILayoutUtility::Internal_MoveWindow_Injected
UnityEngine.GUIStyle::IsTooltipActive
UnityEngine.GUIStyle::get_stretchHeight
UnityEngine.GUIStyle::get_stretchWidth
UnityEngine.GUIStyle::get_wordWrap
UnityEngine.GUIStyle::Internal_GetCursorStringIndex_Injected
UnityEngine.GUIStyle::GetRectOffsetPtr
UnityEngine.GUIStyle::GetStyleStatePtr
UnityEngine.GUIStyle::Internal_Copy
UnityEngine.GUIStyle::Internal_Create
UnityEngine.GUIStyle::Internal_CalcHeight
UnityEngine.GUIStyle::Internal_GetLineHeight
UnityEngine.GUIStyle::get_fixedHeight
UnityEngine.GUIStyle::get_fixedWidth
UnityEngine.GUIStyle::Internal_GetSelectedRenderedText_Injected
UnityEngine.GUIStyle::get_rawName
UnityEngine.GUIStyle::Internal_CalcMinMaxWidth_Injected
UnityEngine.GUIStyle::Internal_CalcSizeWithConstraints_Injected
UnityEngine.GUIStyle::Internal_CalcSize_Injected
UnityEngine.GUIStyle::Internal_Destroy
UnityEngine.GUIStyle::Internal_Draw2_Injected
UnityEngine.GUIStyle::Internal_Draw_Injected
UnityEngine.GUIStyle::Internal_GetCursorPixelPosition_Injected
UnityEngine.GUIStyle::SetDefaultFont
UnityEngine.GUIStyle::SetMouseTooltip_Injected
UnityEngine.GUIStyle::set_alignment
UnityEngine.GUIStyle::set_clipping
UnityEngine.GUIStyle::set_font
UnityEngine.GUIStyle::set_fontSize
UnityEngine.GUIStyle::set_fontStyle
UnityEngine.GUIStyle::set_rawName
UnityEngine.GUIStyle::set_richText
UnityEngine.GUIStyle::set_stretchHeight
UnityEngine.GUIStyle::set_wordWrap
UnityEngine.GUIStyle::get_font
UnityEngine.GUIStyle::get_imagePosition
UnityEngine.GUIStyleState::Init
UnityEngine.GUIStyleState::Cleanup
UnityEngine.GUIStyleState::set_textColor_Injected
UnityEngine.GUIUtility::HasFocusableControls
UnityEngine.GUIUtility::OwnsId
UnityEngine.GUIUtility::get_textFieldInput
UnityEngine.GUIUtility::CheckForTabEvent
UnityEngine.GUIUtility::Internal_GetControlID_Injected
UnityEngine.GUIUtility::Internal_GetHotControl
UnityEngine.GUIUtility::Internal_GetKeyboardControl
UnityEngine.GUIUtility::get_guiDepth
UnityEngine.GUIUtility::Internal_GetDefaultSkin
UnityEngine.GUIUtility::get_pixelsPerPoint
UnityEngine.GUIUtility::get_compositionString
UnityEngine.GUIUtility::get_systemCopyBuffer
UnityEngine.GUIUtility::BeginContainer
UnityEngine.GUIUtility::BeginContainerFromOwner
UnityEngine.GUIUtility::Internal_EndContainer
UnityEngine.GUIUtility::Internal_ExitGUI
UnityEngine.GUIUtility::Internal_SetHotControl
UnityEngine.GUIUtility::Internal_SetKeyboardControl
UnityEngine.GUIUtility::SetKeyboardControlToFirstControlId
UnityEngine.GUIUtility::SetKeyboardControlToLastControlId
UnityEngine.GUIUtility::set_compositionCursorPos_Injected
UnityEngine.GUIUtility::set_imeCompositionMode
UnityEngine.GUIUtility::set_mouseUsed
UnityEngine.GUIUtility::set_systemCopyBuffer
UnityEngine.Hash128::Hash128ToStringImpl_Injected
UnityEngine.ImageConversion::LoadImage
UnityEngine.Input::CheckDisabled
UnityEngine.Input::GetButtonDown
UnityEngine.Input::GetKeyDownInt
UnityEngine.Input::GetKeyInt
UnityEngine.Input::GetMouseButton
UnityEngine.Input::GetMouseButtonDown
UnityEngine.Input::GetMouseButtonUp
UnityEngine.Input::get_anyKey
UnityEngine.Input::get_mousePresent
UnityEngine.Input::get_touchSupported
UnityEngine.Input::get_touchCount
UnityEngine.Input::GetAxis
UnityEngine.Input::GetAxisRaw
UnityEngine.Input::get_compositionString
UnityEngine.Input::GetTouch_Injected
UnityEngine.Input::get_compositionCursorPos_Injected
UnityEngine.Input::get_mousePosition_Injected
UnityEngine.Input::get_mouseScrollDelta_Injected
UnityEngine.Input::set_compositionCursorPos_Injected
UnityEngine.Input::set_imeCompositionMode
UnityEngine.Input::get_imeCompositionMode
UnityEngine.IntegratedSubsystem::SetHandle
UnityEngine.Jobs.TransformAccessArray::GetLength
UnityEngine.Jobs.TransformAccessArray::Create
UnityEngine.Jobs.TransformAccessArray::Add
UnityEngine.Jobs.TransformAccessArray::DestroyTransformAccessArray
UnityEngine.Jobs.TransformAccessArray::GetTransform
UnityEngine.Joint::get_anchor_Injected
UnityEngine.Joint::get_axis_Injected
UnityEngine.Joint::set_anchor_Injected
UnityEngine.Joint::set_autoConfigureConnectedAnchor
UnityEngine.Joint::set_axis_Injected
UnityEngine.Joint::set_connectedAnchor_Injected
UnityEngine.Joint::set_connectedBody
UnityEngine.Joint::set_connectedMassScale
UnityEngine.Joint::get_connectedBody
UnityEngine.JsonUtility::FromJsonInternal
UnityEngine.JsonUtility::ToJsonInternal
UnityEngine.LayerMask::NameToLayer
UnityEngine.LensFlare::set_brightness
UnityEngine.LensFlare::set_color_Injected
UnityEngine.LensFlare::set_fadeSpeed
UnityEngine.LensFlare::set_flare
UnityEngine.Light::get_useColorTemperature
UnityEngine.Light::get_bounceIntensity
UnityEngine.Light::get_colorTemperature
UnityEngine.Light::get_cookieSize
UnityEngine.Light::get_intensity
UnityEngine.Light::get_range
UnityEngine.Light::get_shadowStrength
UnityEngine.Light::get_spotAngle
UnityEngine.Light::get_bakingOutput_Injected
UnityEngine.Light::get_color_Injected
UnityEngine.Light::set_color_Injected
UnityEngine.Light::set_flare
UnityEngine.Light::set_intensity
UnityEngine.Light::set_range
UnityEngine.Light::set_renderMode
UnityEngine.Light::set_shadowStrength
UnityEngine.Light::set_spotAngle
UnityEngine.Light::get_flare
UnityEngine.Light::get_shadows
UnityEngine.Light::get_type
UnityEngine.Light::get_cookie
UnityEngine.LineRenderer::SetPosition_Injected
UnityEngine.LineRenderer::set_endColor_Injected
UnityEngine.LineRenderer::set_startColor_Injected
UnityEngine.Material::HasProperty
UnityEngine.Material::SetPass
UnityEngine.Material::get_enableInstancing
UnityEngine.Material::ComputeCRC
UnityEngine.Material::GetFirstPropertyNameIdByAttribute
UnityEngine.Material::get_passCount
UnityEngine.Material::get_rawRenderQueue
UnityEngine.Material::GetFloatImpl
UnityEngine.Material::GetTagImpl
UnityEngine.Material::GetShaderKeywords
UnityEngine.Material::CopyPropertiesFromMaterial
UnityEngine.Material::CreateWithMaterial
UnityEngine.Material::CreateWithShader
UnityEngine.Material::CreateWithString
UnityEngine.Material::DisableKeyword
UnityEngine.Material::EnableKeyword
UnityEngine.Material::GetColorImpl_Injected
UnityEngine.Material::GetTextureScaleAndOffsetImpl_Injected
UnityEngine.Material::SetColorImpl_Injected
UnityEngine.Material::SetConstantBufferImpl
UnityEngine.Material::SetFloatImpl
UnityEngine.Material::SetMatrixImpl_Injected
UnityEngine.Material::SetShaderKeywords
UnityEngine.Material::SetTextureImpl
UnityEngine.Material::SetTextureOffsetImpl_Injected
UnityEngine.Material::SetTextureScaleImpl_Injected
UnityEngine.Material::set_enableInstancing
UnityEngine.Material::set_renderQueue
UnityEngine.Material::GetTextureImpl
UnityEngine.MaterialPropertyBlock::CreateImpl
UnityEngine.MaterialPropertyBlock::Clear
UnityEngine.MaterialPropertyBlock::DestroyImpl
UnityEngine.MaterialPropertyBlock::SetFloatArrayImpl
UnityEngine.MaterialPropertyBlock::SetFloatImpl
UnityEngine.MaterialPropertyBlock::SetRenderTextureImpl
UnityEngine.MaterialPropertyBlock::SetTextureImpl
UnityEngine.MaterialPropertyBlock::SetVectorArrayImpl
UnityEngine.MaterialPropertyBlock::SetVectorImpl_Injected
UnityEngine.Mathf::IsPowerOfTwo
UnityEngine.Mathf::NextPowerOfTwo
UnityEngine.Mathf::GammaToLinearSpace
UnityEngine.Mathf::LinearToGammaSpace
UnityEngine.Mathf::PerlinNoise
UnityEngine.Mathf::FloatToHalf
UnityEngine.Mathf::CorrelatedColorTemperatureToRGB_Injected
UnityEngine.Matrix4x4::Inverse3DAffine_Injected
UnityEngine.Matrix4x4::GetLossyScale_Injected
UnityEngine.Matrix4x4::Inverse_Injected
UnityEngine.Matrix4x4::LookAt_Injected
UnityEngine.Matrix4x4::Perspective_Injected
UnityEngine.Matrix4x4::TRS_Injected
UnityEngine.Mesh::GetAllocArrayFromChannelImpl
UnityEngine.Mesh::HasVertexAttribute
UnityEngine.Mesh::get_canAccess
UnityEngine.Mesh::get_isReadable
UnityEngine.Mesh::get_subMeshCount
UnityEngine.Mesh::get_vertexCount
UnityEngine.Mesh::GetIndicesImpl
UnityEngine.Mesh::ClearImpl
UnityEngine.Mesh::CombineMeshesImpl
UnityEngine.Mesh::GetArrayFromChannelImpl
UnityEngine.Mesh::Internal_Create
UnityEngine.Mesh::MarkDynamicImpl
UnityEngine.Mesh::PrintErrorCantAccessChannel
UnityEngine.Mesh::RecalculateBoundsImpl
UnityEngine.Mesh::RecalculateNormalsImpl
UnityEngine.Mesh::SetArrayForChannelImpl
UnityEngine.Mesh::SetIndicesImpl
UnityEngine.Mesh::get_bounds_Injected
UnityEngine.Mesh::set_bounds_Injected
UnityEngine.Mesh::set_indexFormat
UnityEngine.Mesh::FromInstanceID
UnityEngine.MeshCollider::set_convex
UnityEngine.MeshCollider::set_sharedMesh
UnityEngine.MeshFilter::set_sharedMesh
UnityEngine.MeshFilter::get_mesh
UnityEngine.MeshFilter::get_sharedMesh
UnityEngine.MonoBehaviour::Internal_IsInvokingAll
UnityEngine.MonoBehaviour::IsInvoking
UnityEngine.MonoBehaviour::IsObjectMonoBehaviour
UnityEngine.MonoBehaviour::get_useGUILayout
UnityEngine.MonoBehaviour::GetScriptClassName
UnityEngine.MonoBehaviour::CancelInvoke
UnityEngine.MonoBehaviour::Internal_CancelInvokeAll
UnityEngine.MonoBehaviour::InvokeDelayed
UnityEngine.MonoBehaviour::StopAllCoroutines
UnityEngine.MonoBehaviour::StopCoroutine
UnityEngine.MonoBehaviour::StopCoroutineFromEnumeratorManaged
UnityEngine.MonoBehaviour::StopCoroutineManaged
UnityEngine.MonoBehaviour::set_useGUILayout
UnityEngine.MonoBehaviour::StartCoroutineManaged
UnityEngine.MonoBehaviour::StartCoroutineManaged2
UnityEngine.Motion::get_isLooping
UnityEngine.NoAllocHelpers::ExtractArrayFromList
UnityEngine.NoAllocHelpers::Internal_ResizeList
UnityEngine.Object::GetOffsetOfInstanceIDInCPlusPlusObject
UnityEngine.Object::GetName
UnityEngine.Object::ToString
UnityEngine.Object::Destroy
UnityEngine.Object::DestroyImmediate
UnityEngine.Object::DontDestroyOnLoad
UnityEngine.Object::SetName
UnityEngine.Object::set_hideFlags
UnityEngine.Object::get_hideFlags
UnityEngine.Object::FindObjectFromInstanceID
UnityEngine.Object::Internal_CloneSingle
UnityEngine.Object::Internal_CloneSingleWithParent
UnityEngine.Object::Internal_InstantiateSingleWithParent_Injected
UnityEngine.Object::Internal_InstantiateSingle_Injected
UnityEngine.Object::FindObjectsOfType
UnityEngine.ObjectGUIState::Internal_Create
UnityEngine.ObjectGUIState::Internal_Destroy
UnityEngine.ParticleSystem::get_isPlaying
UnityEngine.ParticleSystem::get_useAutoRandomSeed
UnityEngine.ParticleSystem::get_time
UnityEngine.ParticleSystem::EmitOld_Internal
UnityEngine.ParticleSystem::Emit_Injected
UnityEngine.ParticleSystem::Emit_Internal
UnityEngine.ParticleSystem::Play
UnityEngine.ParticleSystem::Simulate
UnityEngine.ParticleSystem::Stop
UnityEngine.ParticleSystem::set_randomSeed
UnityEngine.ParticleSystem::set_useAutoRandomSeed
UnityEngine.ParticleSystem/EmissionModule::get_enabled_Injected
UnityEngine.ParticleSystem/EmissionModule::set_enabled_Injected
UnityEngine.ParticleSystem/EmissionModule::set_rateOverTimeMultiplier_Injected
UnityEngine.ParticleSystem/EmissionModule::set_rateOverTime_Injected
UnityEngine.ParticleSystem/MainModule::get_loop_Injected
UnityEngine.ParticleSystem/MainModule::get_duration_Injected
UnityEngine.ParticleSystem/MainModule::get_startColor_Injected
UnityEngine.ParticleSystem/MainModule::set_startColor_Injected
UnityEngine.ParticleSystem/MainModule::set_startSize_Injected
UnityEngine.ParticleSystem/MainModule::set_startSpeed_Injected
UnityEngine.ParticleSystem/SubEmittersModule::get_subEmittersCount_Injected
UnityEngine.ParticleSystem/SubEmittersModule::GetSubEmitterSystem_Injected
UnityEngine.ParticleSystemRenderer::GetMeshes
UnityEngine.PhysicMaterial::Internal_CreateDynamicsMaterial
UnityEngine.Physics::Query_ComputePenetration_Injected
UnityEngine.Physics::IgnoreCollision
UnityEngine.Physics::get_defaultPhysicsScene_Injected
UnityEngine.Physics::OverlapSphere_Internal_Injected
UnityEngine.Physics::Internal_RaycastAll_Injected
UnityEngine.Physics2D::get_queriesHitTriggers
UnityEngine.Physics2D::GetRayIntersectionAll_Internal_Injected
UnityEngine.PhysicsScene::Internal_RaycastTest_Injected
UnityEngine.PhysicsScene::Internal_Raycast_Injected
UnityEngine.PhysicsScene::Query_CapsuleCast_Injected
UnityEngine.PhysicsScene::Internal_RaycastNonAlloc_Injected
UnityEngine.PhysicsScene::Internal_SphereCastNonAlloc_Injected
UnityEngine.PhysicsScene::OverlapSphereNonAlloc_Internal_Injected
UnityEngine.PhysicsScene2D::GetRayIntersectionArray_Internal_Injected
UnityEngine.PhysicsScene2D::RaycastArray_Internal_Injected
UnityEngine.PhysicsScene2D::RaycastList_Internal_Injected
UnityEngine.PhysicsScene2D::GetRayIntersection_Internal_Injected
UnityEngine.PhysicsScene2D::Raycast_Internal_Injected
UnityEngine.Playables.PlayableDirector::get_duration
UnityEngine.Playables.PlayableDirector::get_time
UnityEngine.Playables.PlayableDirector::Evaluate
UnityEngine.Playables.PlayableDirector::GetGraphHandle_Injected
UnityEngine.Playables.PlayableDirector::Pause
UnityEngine.Playables.PlayableDirector::Play
UnityEngine.Playables.PlayableDirector::PlayOnFrame_Injected
UnityEngine.Playables.PlayableDirector::Stop
UnityEngine.Playables.PlayableDirector::set_time
UnityEngine.Playables.PlayableDirector::GetGenericBinding
UnityEngine.Playables.PlayableDirector::GetReferenceValue_Injected
UnityEngine.Playables.PlayableDirector::GetWrapMode
UnityEngine.Playables.PlayableDirector::GetPlayState
UnityEngine.Playables.PlayableDirector::Internal_GetPlayableAsset
UnityEngine.Playables.PlayableGraph::ConnectInternal_Injected
UnityEngine.Playables.PlayableGraph::CreateScriptOutputInternal_Injected
UnityEngine.Playables.PlayableGraph::IsMatchFrameRateEnabled_Injected
UnityEngine.Playables.PlayableGraph::IsPlaying_Injected
UnityEngine.Playables.PlayableGraph::IsValid_Injected
UnityEngine.Playables.PlayableGraph::GetPlayableCount_Injected
UnityEngine.Playables.PlayableGraph::GetRootPlayableCount_Injected
UnityEngine.Playables.PlayableGraph::CreatePlayableHandle_Injected
UnityEngine.Playables.PlayableGraph::Evaluate_Injected
UnityEngine.Playables.PlayableGraph::GetFrameRate_Injected
UnityEngine.Playables.PlayableGraph::GetRootPlayableInternal_Injected
UnityEngine.Playables.PlayableGraph::SynchronizeEvaluation_Injected
UnityEngine.Playables.PlayableGraph::GetResolver_Injected
UnityEngine.Playables.PlayableHandle::IsDone_Injected
UnityEngine.Playables.PlayableHandle::IsValid_Injected
UnityEngine.Playables.PlayableHandle::GetDuration_Injected
UnityEngine.Playables.PlayableHandle::GetPreviousTime_Injected
UnityEngine.Playables.PlayableHandle::GetTime_Injected
UnityEngine.Playables.PlayableHandle::GetInputCount_Injected
UnityEngine.Playables.PlayableHandle::GetScriptInstance_Injected
UnityEngine.Playables.PlayableHandle::GetInputWeightFromIndex_Injected
UnityEngine.Playables.PlayableHandle::GetPlayableType_Injected
UnityEngine.Playables.PlayableHandle::GetGraph_Injected
UnityEngine.Playables.PlayableHandle::GetInputHandle_Injected
UnityEngine.Playables.PlayableHandle::Pause_Injected
UnityEngine.Playables.PlayableHandle::Play_Injected
UnityEngine.Playables.PlayableHandle::SetDone_Injected
UnityEngine.Playables.PlayableHandle::SetDuration_Injected
UnityEngine.Playables.PlayableHandle::SetInputCount_Injected
UnityEngine.Playables.PlayableHandle::SetInputWeightFromIndex_Injected
UnityEngine.Playables.PlayableHandle::SetInputWeight_Injected
UnityEngine.Playables.PlayableHandle::SetPropagateSetTime_Injected
UnityEngine.Playables.PlayableHandle::SetScriptInstance_Injected
UnityEngine.Playables.PlayableHandle::SetSpeed_Injected
UnityEngine.Playables.PlayableHandle::SetTimeWrapMode_Injected
UnityEngine.Playables.PlayableHandle::SetTime_Injected
UnityEngine.Playables.PlayableHandle::SetTraversalMode_Injected
UnityEngine.Playables.PlayableHandle::GetTimeWrapMode_Injected
UnityEngine.Playables.PlayableHandle::GetPlayState_Injected
UnityEngine.Playables.PlayableOutputHandle::IsValid_Injected
UnityEngine.Playables.PlayableOutputHandle::GetSourceOutputPort_Injected
UnityEngine.Playables.PlayableOutputHandle::GetPlayableOutputType_Injected
UnityEngine.Playables.PlayableOutputHandle::AddNotificationReceiver_Injected
UnityEngine.Playables.PlayableOutputHandle::GetSourcePlayable_Injected
UnityEngine.Playables.PlayableOutputHandle::PushNotification_Injected
UnityEngine.Playables.PlayableOutputHandle::SetReferenceObject_Injected
UnityEngine.Playables.PlayableOutputHandle::SetSourcePlayable_Injected
UnityEngine.Playables.PlayableOutputHandle::SetUserData_Injected
UnityEngine.Playables.PlayableOutputHandle::SetWeight_Injected
UnityEngine.PlayerConnectionInternal::IsConnected
UnityEngine.PlayerConnectionInternal::TrySendMessage
UnityEngine.PlayerConnectionInternal::DisconnectAll
UnityEngine.PlayerConnectionInternal::Initialize
UnityEngine.PlayerConnectionInternal::PollInternal
UnityEngine.PlayerConnectionInternal::RegisterInternal
UnityEngine.PlayerConnectionInternal::SendMessage
UnityEngine.PlayerConnectionInternal::UnregisterInternal
UnityEngine.PlayerPrefs::HasKey
UnityEngine.PlayerPrefs::TrySetFloat
UnityEngine.PlayerPrefs::TrySetInt
UnityEngine.PlayerPrefs::TrySetSetString
UnityEngine.PlayerPrefs::GetInt
UnityEngine.PlayerPrefs::GetFloat
UnityEngine.PlayerPrefs::GetString
UnityEngine.PlayerPrefs::DeleteKey
UnityEngine.PlayerPrefs::Save
UnityEngine.PolygonCollider2D::GetTotalPointCount
UnityEngine.PolygonCollider2D::get_pathCount
UnityEngine.PolygonCollider2D::GetPath_Internal
UnityEngine.Profiling.Profiler::GetRuntimeMemorySizeLong
UnityEngine.PropertyNameUtils::PropertyNameFromString_Injected
UnityEngine.QualitySettings::GetQualityLevel
UnityEngine.QualitySettings::SetQualityLevel
UnityEngine.QualitySettings::get_activeColorSpace
UnityEngine.Quaternion::AngleAxis_Injected
UnityEngine.Quaternion::FromToRotation_Injected
UnityEngine.Quaternion::Internal_FromEulerRad_Injected
UnityEngine.Quaternion::Internal_ToEulerRad_Injected
UnityEngine.Quaternion::Inverse_Injected
UnityEngine.Quaternion::Lerp_Injected
UnityEngine.Quaternion::LookRotation_Injected
UnityEngine.Quaternion::SlerpUnclamped_Injected
UnityEngine.Quaternion::Slerp_Injected
UnityEngine.Random::RandomRangeInt
UnityEngine.Random::Range
UnityEngine.Random::get_value
UnityEngine.Random::InitState
UnityEngine.Random::get_state_Injected
UnityEngine.Random::set_state_Injected
UnityEngine.RectOffset::get_bottom
UnityEngine.RectOffset::get_horizontal
UnityEngine.RectOffset::get_left
UnityEngine.RectOffset::get_right
UnityEngine.RectOffset::get_top
UnityEngine.RectOffset::get_vertical
UnityEngine.RectOffset::InternalCreate
UnityEngine.RectOffset::InternalDestroy
UnityEngine.RectOffset::Remove_Injected
UnityEngine.RectOffset::set_bottom
UnityEngine.RectOffset::set_left
UnityEngine.RectOffset::set_right
UnityEngine.RectOffset::set_top
UnityEngine.RectTransform::ForceUpdateRectTransforms
UnityEngine.RectTransform::get_anchorMax_Injected
UnityEngine.RectTransform::get_anchorMin_Injected
UnityEngine.RectTransform::get_anchoredPosition_Injected
UnityEngine.RectTransform::get_pivot_Injected
UnityEngine.RectTransform::get_rect_Injected
UnityEngine.RectTransform::get_sizeDelta_Injected
UnityEngine.RectTransform::set_anchorMax_Injected
UnityEngine.RectTransform::set_anchorMin_Injected
UnityEngine.RectTransform::set_anchoredPosition_Injected
UnityEngine.RectTransform::set_pivot_Injected
UnityEngine.RectTransform::set_sizeDelta_Injected
UnityEngine.RectTransformUtility::PointInRectangle_Injected
UnityEngine.RectTransformUtility::PixelAdjustPoint_Injected
UnityEngine.RectTransformUtility::PixelAdjustRect_Injected
UnityEngine.Renderer::get_enabled
UnityEngine.Renderer::get_sortingGroupID
UnityEngine.Renderer::get_sortingGroupOrder
UnityEngine.Renderer::get_sortingLayerID
UnityEngine.Renderer::get_sortingOrder
UnityEngine.Renderer::SetMaterial
UnityEngine.Renderer::SetMaterialArray
UnityEngine.Renderer::get_bounds_Injected
UnityEngine.Renderer::set_enabled
UnityEngine.Renderer::set_receiveShadows
UnityEngine.Renderer::set_shadowCastingMode
UnityEngine.Renderer::set_sortingLayerID
UnityEngine.Renderer::set_sortingOrder
UnityEngine.Renderer::GetMaterial
UnityEngine.Renderer::GetSharedMaterial
UnityEngine.Renderer::GetMaterialArray
UnityEngine.Renderer::GetSharedMaterialArray
UnityEngine.Rendering.CommandBuffer::ValidateAgainstExecutionFlags
UnityEngine.Rendering.CommandBuffer::CreateGPUFence_Internal
UnityEngine.Rendering.CommandBuffer::InitBuffer
UnityEngine.Rendering.CommandBuffer::BeginSample
UnityEngine.Rendering.CommandBuffer::BeginSample_CustomSampler
UnityEngine.Rendering.CommandBuffer::Clear
UnityEngine.Rendering.CommandBuffer::ClearRenderTarget_Injected
UnityEngine.Rendering.CommandBuffer::DisableShaderKeyword
UnityEngine.Rendering.CommandBuffer::EnableShaderKeyword
UnityEngine.Rendering.CommandBuffer::EndSample
UnityEngine.Rendering.CommandBuffer::EndSample_CustomSampler
UnityEngine.Rendering.CommandBuffer::InternalSetComputeBufferData
UnityEngine.Rendering.CommandBuffer::Internal_DispatchCompute
UnityEngine.Rendering.CommandBuffer::Internal_DrawMesh_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawOcclusionMesh_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawProcedural_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawRendererList_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeConstantComputeBufferParam
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeTextureParam
UnityEngine.Rendering.CommandBuffer::ReleaseBuffer
UnityEngine.Rendering.CommandBuffer::SetExecutionFlags
UnityEngine.Rendering.CommandBuffer::SetGlobalConstantBufferInternal
UnityEngine.Rendering.CommandBuffer::SetGlobalTexture_Impl
UnityEngine.Rendering.CommandBuffer::SetGlobalVector_Injected
UnityEngine.Rendering.CommandBuffer::SetRenderTargetColorDepth_Internal_Injected
UnityEngine.Rendering.CommandBuffer::SetRenderTargetMultiSubtarget_Injected
UnityEngine.Rendering.CommandBuffer::SetRenderTargetSingle_Internal_Injected
UnityEngine.Rendering.CommandBuffer::SetViewport_Injected
UnityEngine.Rendering.CommandBuffer::WaitOnGPUFence_Internal
UnityEngine.Rendering.CommandBuffer::set_name
UnityEngine.Rendering.CommandBufferExtensions::Internal_SwitchIntoFastMemory
UnityEngine.Rendering.CommandBufferExtensions::Internal_SwitchOutOfFastMemory
UnityEngine.Rendering.GraphicsFence::GetVersionNumber
UnityEngine.Rendering.GraphicsSettings::get_lightsUseLinearIntensity
UnityEngine.Rendering.GraphicsSettings::set_INTERNAL_defaultRenderPipeline
UnityEngine.Rendering.RendererUtils.RendererList::get_isValid_Injected
UnityEngine.Rendering.ScriptableRenderContext::CreateRendererList_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::DrawRenderers_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::ExecuteCommandBufferAsync_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::ExecuteCommandBuffer_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::GetCameras_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::InitializeSortSettings
UnityEngine.Rendering.ScriptableRenderContext::PrepareRendererListsAsync_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::QueryRendererListStatus_Internal_Injected
UnityEngine.Rendering.ShaderKeyword::GetGlobalKeywordCount
UnityEngine.Rendering.ShaderKeyword::GetGlobalKeywordIndex
UnityEngine.Rendering.ShaderKeyword::CreateGlobalKeyword
UnityEngine.Rendering.SortingGroup::get_invalidSortingGroupID
UnityEngine.Rendering.SortingGroup::get_sortingLayerID
UnityEngine.Rendering.SortingGroup::get_sortingOrder
UnityEngine.Rendering.SortingGroup::GetSortingGroupByIndex
UnityEngine.RenderSettings::set_ambientEquatorColor_Injected
UnityEngine.RenderSettings::set_ambientSkyColor_Injected
UnityEngine.RenderSettings::set_fog
UnityEngine.RenderSettings::set_skybox
UnityEngine.RenderTexture::Create
UnityEngine.RenderTexture::get_useDynamicScale
UnityEngine.RenderTexture::get_useMipMap
UnityEngine.RenderTexture::get_antiAliasing
UnityEngine.RenderTexture::get_height
UnityEngine.RenderTexture::get_volumeDepth
UnityEngine.RenderTexture::get_width
UnityEngine.RenderTexture::GetColorBuffer_Injected
UnityEngine.RenderTexture::GetDepthBuffer_Injected
UnityEngine.RenderTexture::GetDescriptor_Injected
UnityEngine.RenderTexture::Internal_Create
UnityEngine.RenderTexture::Release
UnityEngine.RenderTexture::ReleaseTemporary
UnityEngine.RenderTexture::SetActive
UnityEngine.RenderTexture::SetMipMapCount
UnityEngine.RenderTexture::SetRenderTextureDescriptor_Injected
UnityEngine.RenderTexture::SetSRGBReadWrite
UnityEngine.RenderTexture::set_antiAliasing
UnityEngine.RenderTexture::set_autoGenerateMips
UnityEngine.RenderTexture::set_bindTextureMS
UnityEngine.RenderTexture::set_depthStencilFormat
UnityEngine.RenderTexture::set_dimension
UnityEngine.RenderTexture::set_enableRandomWrite
UnityEngine.RenderTexture::set_graphicsFormat
UnityEngine.RenderTexture::set_height
UnityEngine.RenderTexture::set_memorylessMode
UnityEngine.RenderTexture::set_stencilFormat
UnityEngine.RenderTexture::set_useDynamicScale
UnityEngine.RenderTexture::set_useMipMap
UnityEngine.RenderTexture::set_volumeDepth
UnityEngine.RenderTexture::set_width
UnityEngine.RenderTexture::get_graphicsFormat
UnityEngine.RenderTexture::GetActive
UnityEngine.RenderTexture::GetTemporary_Internal_Injected
UnityEngine.RenderTexture::get_dimension
UnityEngine.Resources::GetBuiltinResource
UnityEngine.ResourcesAPIInternal::Load
UnityEngine.ResourcesAPIInternal::FindObjectsOfTypeAll
UnityEngine.ResourcesAPIInternal::FindShaderByName
UnityEngine.Rigidbody::get_isKinematic
UnityEngine.Rigidbody::get_drag
UnityEngine.Rigidbody::get_mass
UnityEngine.Rigidbody::AddForceAtPosition_Injected
UnityEngine.Rigidbody::AddForce_Injected
UnityEngine.Rigidbody::AddRelativeForce_Injected
UnityEngine.Rigidbody::AddRelativeTorque_Injected
UnityEngine.Rigidbody::MovePosition_Injected
UnityEngine.Rigidbody::MoveRotation_Injected
UnityEngine.Rigidbody::ResetInertiaTensor
UnityEngine.Rigidbody::get_angularVelocity_Injected
UnityEngine.Rigidbody::get_position_Injected
UnityEngine.Rigidbody::get_rotation_Injected
UnityEngine.Rigidbody::get_velocity_Injected
UnityEngine.Rigidbody::set_angularDrag
UnityEngine.Rigidbody::set_angularVelocity_Injected
UnityEngine.Rigidbody::set_centerOfMass_Injected
UnityEngine.Rigidbody::set_collisionDetectionMode
UnityEngine.Rigidbody::set_detectCollisions
UnityEngine.Rigidbody::set_drag
UnityEngine.Rigidbody::set_freezeRotation
UnityEngine.Rigidbody::set_interpolation
UnityEngine.Rigidbody::set_isKinematic
UnityEngine.Rigidbody::set_mass
UnityEngine.Rigidbody::set_maxAngularVelocity
UnityEngine.Rigidbody::set_rotation_Injected
UnityEngine.Rigidbody::set_useGravity
UnityEngine.Rigidbody::set_velocity_Injected
UnityEngine.Rigidbody2D::get_mass
UnityEngine.Rigidbody2D::get_rotation
UnityEngine.Rigidbody2D::MovePosition_Injected
UnityEngine.Rigidbody2D::MoveRotation_Angle
UnityEngine.Rigidbody2D::get_position_Injected
UnityEngine.Rigidbody2D::get_velocity_Injected
UnityEngine.Rigidbody2D::set_position_Injected
UnityEngine.ScalableBufferManager::get_heightScaleFactor
UnityEngine.ScalableBufferManager::get_widthScaleFactor
UnityEngine.ScalableBufferManager::ResizeBuffers
UnityEngine.SceneManagement.Scene::GetBuildIndexInternal
UnityEngine.SceneManagement.Scene::GetGUIDInternal
UnityEngine.SceneManagement.Scene::GetNameInternal
UnityEngine.SceneManagement.SceneManager::get_sceneCount
UnityEngine.SceneManagement.SceneManager::GetActiveScene_Injected
UnityEngine.SceneManagement.SceneManager::GetSceneAt_Injected
UnityEngine.SceneManagement.SceneManagerAPIInternal::LoadSceneAsyncNameIndexInternal_Injected
UnityEngine.Screen::get_fullScreen
UnityEngine.Screen::get_height
UnityEngine.Screen::get_width
UnityEngine.Screen::get_dpi
UnityEngine.Screen::get_safeArea_Injected
UnityEngine.Screen::set_sleepTimeout
UnityEngine.Screen::GetScreenOrientation
UnityEngine.ScriptableObject::CreateScriptableObject
UnityEngine.ScriptableObject::CreateScriptableObjectInstanceFromType
UnityEngine.ScriptingRuntime::GetAllUserAssemblies
UnityEngine.Shader::PropertyToID
UnityEngine.Shader::TagToID
UnityEngine.Shader::DisableKeyword
UnityEngine.Shader::EnableKeyword
UnityEngine.Shader::SetGlobalConstantBufferImpl
UnityEngine.SortingLayer::GetLayerValueFromID
UnityEngine.SphereCollider::set_radius
UnityEngine.Sprite::GetPacked
UnityEngine.Sprite::GetPackingMode
UnityEngine.Sprite::GetPackingRotation
UnityEngine.Sprite::get_pixelsPerUnit
UnityEngine.Sprite::get_triangles
UnityEngine.Sprite::GetInnerUVs_Injected
UnityEngine.Sprite::GetOuterUVs_Injected
UnityEngine.Sprite::GetPadding_Injected
UnityEngine.Sprite::GetTextureRect_Injected
UnityEngine.Sprite::get_border_Injected
UnityEngine.Sprite::get_bounds_Injected
UnityEngine.Sprite::get_pivot_Injected
UnityEngine.Sprite::get_rect_Injected
UnityEngine.Sprite::CreateSprite_Injected
UnityEngine.Sprite::get_associatedAlphaSplitTexture
UnityEngine.Sprite::get_texture
UnityEngine.Sprite::get_uv
UnityEngine.Sprite::get_vertices
UnityEngine.SpriteRenderer::get_color_Injected
UnityEngine.SpriteRenderer::set_color_Injected
UnityEngine.SubsystemDescriptorBindings::GetId
UnityEngine.SubsystemManager::StaticConstructScriptingClassMap
UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::ReportSingleSubsystemAnalytics
UnityEngine.SystemInfo::GetGraphicsUVStartsAtTop
UnityEngine.SystemInfo::IsFormatSupported
UnityEngine.SystemInfo::SupportsGPUFence
UnityEngine.SystemInfo::SupportsInstancing
UnityEngine.SystemInfo::SupportsTextureFormatNative
UnityEngine.SystemInfo::UsesReversedZBuffer
UnityEngine.SystemInfo::GetGraphicsShaderLevel
UnityEngine.SystemInfo::GetMaxRenderTextureSize
UnityEngine.SystemInfo::GetMaxTextureSize
UnityEngine.SystemInfo::GetPhysicalMemoryMB
UnityEngine.SystemInfo::SupportedRenderTargetCount
UnityEngine.SystemInfo::MaxGraphicsBufferSize
UnityEngine.SystemInfo::GetDeviceModel
UnityEngine.SystemInfo::GetDeviceName
UnityEngine.SystemInfo::GetGraphicsDeviceName
UnityEngine.SystemInfo::GetOperatingSystem
UnityEngine.SystemInfo::GetProcessorType
UnityEngine.SystemInfo::GetCompatibleFormat
UnityEngine.SystemInfo::GetGraphicsFormat
UnityEngine.SystemInfo::GetOperatingSystemFamily
UnityEngine.SystemInfo::GetGraphicsDeviceType
UnityEngine.Terrain::get_allowAutoConnect
UnityEngine.Terrain::get_groupingID
UnityEngine.Terrain::SetNeighbors
UnityEngine.Terrain::get_activeTerrain
UnityEngine.Terrain::get_terrainData
UnityEngine.Terrain::get_activeTerrains
UnityEngine.TerrainData::GetBoundaryValue
UnityEngine.TerrainData::get_Internal_alphamapResolution
UnityEngine.TerrainData::get_alphamapLayers
UnityEngine.TerrainData::GetAlphamapResolutionInternal
UnityEngine.TerrainData::Internal_GetAlphamaps
UnityEngine.TerrainData::Internal_Create
UnityEngine.TerrainData::Internal_SetAlphamaps
UnityEngine.TerrainData::get_size_Injected
UnityEngine.TerrainData::get_users
UnityEngine.TextAsset::get_bytes
UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphToTexture_Internal
UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphsToTexture_Internal
UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithIndexValue_Internal
UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithUnicodeValue_Internal
UnityEngine.TextCore.LowLevel.FontEngine::TryGetSystemFontReference_Internal
UnityEngine.TextCore.LowLevel.FontEngine::GetFaceInfo_Internal
UnityEngine.TextCore.LowLevel.FontEngine::GetPairAdjustmentRecordsFromMarshallingArray
UnityEngine.TextCore.LowLevel.FontEngine::InitializeFontEngine_Internal
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_And_FaceIndex_Internal
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_FromFont_Internal
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal
UnityEngine.TextCore.LowLevel.FontEngine::PopulatePairAdjustmentRecordMarshallingArray_from_KernTable
UnityEngine.TextCore.LowLevel.FontEngine::GetGlyphIndex
UnityEngine.TextCore.LowLevel.FontEngine::ResetAtlasTexture
UnityEngine.TextCore.LowLevel.FontEngine::SetTextureUploadMode
UnityEngine.TextGenerator::Populate_Internal_Injected
UnityEngine.TextGenerator::get_characterCount
UnityEngine.TextGenerator::get_lineCount
UnityEngine.TextGenerator::Internal_Create
UnityEngine.TextGenerator::GetCharactersInternal
UnityEngine.TextGenerator::GetLinesInternal
UnityEngine.TextGenerator::GetVerticesInternal
UnityEngine.TextGenerator::Internal_Destroy
UnityEngine.TextGenerator::get_rectExtents_Injected
UnityEngine.Texture::get_isReadable
UnityEngine.Texture::GetDataHeight
UnityEngine.Texture::GetDataWidth
UnityEngine.Texture::Internal_GetActiveTextureColorSpace
UnityEngine.Texture::get_anisoLevel
UnityEngine.Texture::get_mipmapCount
UnityEngine.Texture::get_updateCount
UnityEngine.Texture::get_texelSize_Injected
UnityEngine.Texture::set_anisoLevel
UnityEngine.Texture::set_filterMode
UnityEngine.Texture::set_mipMapBias
UnityEngine.Texture::set_wrapMode
UnityEngine.Texture::set_wrapModeU
UnityEngine.Texture::set_wrapModeV
UnityEngine.Texture::set_wrapModeW
UnityEngine.Texture::get_filterMode
UnityEngine.Texture::GetDimension
UnityEngine.Texture::get_wrapMode
UnityEngine.Texture2D::Internal_CreateImpl
UnityEngine.Texture2D::ReinitializeImpl
UnityEngine.Texture2D::ReinitializeWithFormatImpl
UnityEngine.Texture2D::get_isReadable
UnityEngine.Texture2D::GetWritableImageData
UnityEngine.Texture2D::GetRawImageDataSize
UnityEngine.Texture2D::ApplyImpl
UnityEngine.Texture2D::GetPixelBilinearImpl_Injected
UnityEngine.Texture2D::SetAllPixels32
UnityEngine.Texture2D::SetPixelImpl_Injected
UnityEngine.Texture2D::SetPixelsImpl
UnityEngine.Texture2D::get_blackTexture
UnityEngine.Texture2D::get_whiteTexture
UnityEngine.Texture2D::get_format
UnityEngine.Texture2DArray::Internal_CreateImpl
UnityEngine.Texture2DArray::get_isReadable
UnityEngine.Texture3D::Internal_CreateImpl
UnityEngine.Texture3D::get_isReadable
UnityEngine.Texture3D::ApplyImpl
UnityEngine.Texture3D::SetPixelImpl_Injected
UnityEngine.Texture3D::SetPixels
UnityEngine.Time::get_frameCount
UnityEngine.Time::get_deltaTime
UnityEngine.Time::get_fixedDeltaTime
UnityEngine.Time::get_fixedUnscaledTime
UnityEngine.Time::get_maximumDeltaTime
UnityEngine.Time::get_realtimeSinceStartup
UnityEngine.Time::get_smoothDeltaTime
UnityEngine.Time::get_time
UnityEngine.Time::get_timeScale
UnityEngine.Time::get_timeSinceLevelLoad
UnityEngine.Time::get_unscaledDeltaTime
UnityEngine.Time::get_unscaledTime
UnityEngine.Time::set_fixedDeltaTime
UnityEngine.Time::set_timeScale
UnityEngine.TouchScreenKeyboard::IsInPlaceEditingAllowed
UnityEngine.TouchScreenKeyboard::get_active
UnityEngine.TouchScreenKeyboard::get_canGetSelection
UnityEngine.TouchScreenKeyboard::get_canSetSelection
UnityEngine.TouchScreenKeyboard::TouchScreenKeyboard_InternalConstructorHelper
UnityEngine.TouchScreenKeyboard::get_text
UnityEngine.TouchScreenKeyboard::GetSelection
UnityEngine.TouchScreenKeyboard::Internal_Destroy
UnityEngine.TouchScreenKeyboard::SetSelection
UnityEngine.TouchScreenKeyboard::set_active
UnityEngine.TouchScreenKeyboard::set_characterLimit
UnityEngine.TouchScreenKeyboard::set_hideInput
UnityEngine.TouchScreenKeyboard::set_text
UnityEngine.TouchScreenKeyboard::get_status
UnityEngine.TrailRenderer::get_endWidth
UnityEngine.TrailRenderer::get_startWidth
UnityEngine.TrailRenderer::get_time
UnityEngine.TrailRenderer::set_emitting
UnityEngine.TrailRenderer::set_endWidth
UnityEngine.TrailRenderer::set_startColor_Injected
UnityEngine.TrailRenderer::set_startWidth
UnityEngine.TrailRenderer::set_time
UnityEngine.Transform::IsChildOf
UnityEngine.Transform::get_hasChanged
UnityEngine.Transform::GetSiblingIndex
UnityEngine.Transform::get_childCount
UnityEngine.Transform::GetPositionAndRotation
UnityEngine.Transform::Internal_LookAt_Injected
UnityEngine.Transform::InverseTransformDirection_Injected
UnityEngine.Transform::InverseTransformPoint_Injected
UnityEngine.Transform::RotateAroundInternal_Injected
UnityEngine.Transform::SetAsFirstSibling
UnityEngine.Transform::SetLocalPositionAndRotation_Injected
UnityEngine.Transform::SetParent
UnityEngine.Transform::SetPositionAndRotation_Injected
UnityEngine.Transform::SetSiblingIndex
UnityEngine.Transform::TransformDirection_Injected
UnityEngine.Transform::TransformPoint_Injected
UnityEngine.Transform::TransformVector_Injected
UnityEngine.Transform::get_localPosition_Injected
UnityEngine.Transform::get_localRotation_Injected
UnityEngine.Transform::get_localScale_Injected
UnityEngine.Transform::get_localToWorldMatrix_Injected
UnityEngine.Transform::get_lossyScale_Injected
UnityEngine.Transform::get_position_Injected
UnityEngine.Transform::get_rotation_Injected
UnityEngine.Transform::get_worldToLocalMatrix_Injected
UnityEngine.Transform::set_hasChanged
UnityEngine.Transform::set_localPosition_Injected
UnityEngine.Transform::set_localRotation_Injected
UnityEngine.Transform::set_localScale_Injected
UnityEngine.Transform::set_position_Injected
UnityEngine.Transform::set_rotation_Injected
UnityEngine.Transform::FindRelativeTransformWithPath
UnityEngine.Transform::GetChild
UnityEngine.Transform::GetParent
UnityEngine.Transform::GetRoot
UnityEngine.U2D.SpriteAtlas::CanBindTo
UnityEngine.U2D.SpriteAtlasManager::Register
UnityEngine.UIElements.TextNative::DoComputeTextHeight_Injected
UnityEngine.UIElements.TextNative::DoComputeTextWidth_Injected
UnityEngine.UIElements.TextNative::DoGetCursorPosition_Injected
UnityEngine.UIElements.TextNative::DoGetOffset_Injected
UnityEngine.UIElements.TextNative::GetVertices_Injected
UnityEngine.UIElements.UIElementsRuntimeUtilityNative::RegisterPlayerloopCallback
UnityEngine.UIElements.UIElementsRuntimeUtilityNative::UnregisterPlayerloopCallback
UnityEngine.UIElements.UIElementsRuntimeUtilityNative::VisualElementCreation
UnityEngine.UIElements.UIR.Utility::CPUFencePassed
UnityEngine.UIElements.UIR.Utility::HasMappedBufferRange
UnityEngine.UIElements.UIR.Utility::AllocateBuffer
UnityEngine.UIElements.UIR.Utility::CreateStencilState_Injected
UnityEngine.UIElements.UIR.Utility::GetVertexDeclaration
UnityEngine.UIElements.UIR.Utility::InsertCPUFence
UnityEngine.UIElements.UIR.Utility::DisableScissor
UnityEngine.UIElements.UIR.Utility::DrawRanges
UnityEngine.UIElements.UIR.Utility::FreeBuffer
UnityEngine.UIElements.UIR.Utility::GetActiveViewport_Injected
UnityEngine.UIElements.UIR.Utility::GetUnityProjectionMatrix_Injected
UnityEngine.UIElements.UIR.Utility::NotifyOfUIREvents
UnityEngine.UIElements.UIR.Utility::ProfileDrawChainBegin
UnityEngine.UIElements.UIR.Utility::ProfileDrawChainEnd
UnityEngine.UIElements.UIR.Utility::RegisterIntermediateRenderer_Injected
UnityEngine.UIElements.UIR.Utility::SetPropertyBlock
UnityEngine.UIElements.UIR.Utility::SetScissorRect_Injected
UnityEngine.UIElements.UIR.Utility::SetStencilState
UnityEngine.UIElements.UIR.Utility::SetVectorArray
UnityEngine.UIElements.UIR.Utility::SyncRenderThread
UnityEngine.UIElements.UIR.Utility::UpdateBufferRanges
UnityEngine.UIElements.UIR.Utility::WaitForCPUFencePassed
UnityEngine.UISystemProfilerApi::AddMarker
UnityEngine.UISystemProfilerApi::BeginSample
UnityEngine.UISystemProfilerApi::EndSample
UnityEngine.UnityLogWriter::WriteStringToUnityLogImpl
UnityEngine.Vector3::Slerp_Injected
UnityEngine.WheelCollider::GetGroundHit
UnityEngine.WheelCollider::get_isGrounded
UnityEngine.WheelCollider::get_brakeTorque
UnityEngine.WheelCollider::get_motorTorque
UnityEngine.WheelCollider::get_radius
UnityEngine.WheelCollider::get_rpm
UnityEngine.WheelCollider::get_steerAngle
UnityEngine.WheelCollider::get_suspensionDistance
UnityEngine.WheelCollider::GetWorldPose
UnityEngine.WheelCollider::get_forwardFriction_Injected
UnityEngine.WheelCollider::get_sidewaysFriction_Injected
UnityEngine.WheelCollider::get_suspensionSpring_Injected
UnityEngine.WheelCollider::set_brakeTorque
UnityEngine.WheelCollider::set_forceAppPointDistance
UnityEngine.WheelCollider::set_forwardFriction_Injected
UnityEngine.WheelCollider::set_mass
UnityEngine.WheelCollider::set_motorTorque
UnityEngine.WheelCollider::set_radius
UnityEngine.WheelCollider::set_sidewaysFriction_Injected
UnityEngine.WheelCollider::set_steerAngle
UnityEngine.WheelCollider::set_suspensionDistance
UnityEngine.WheelCollider::set_suspensionSpring_Injected
UnityEngine.WheelCollider::set_wheelDampingRate
UnityEngine.XR.XRDevice::DisableAutoXRCameraTracking
UnityEngine.XR.XRMeshSubsystem/MeshTransformList::Dispose
UnityEngine.XR.XRSettings::get_enabled
UnityEngine.XR.XRSettings::get_isDeviceActive
UnityEngine.XR.XRSettings::get_eyeTextureHeight
UnityEngine.XR.XRSettings::get_eyeTextureWidth
UnityEngine.XR.XRSettings::get_eyeTextureResolutionScale
UnityEngine.XR.XRSettings::get_renderViewportScaleInternal
UnityEngine.XR.XRSettings::get_loadedDeviceName
UnityEngine.XR.XRSettings::get_supportedDevices
UnityEngine.XR.XRSettings::get_eyeTextureDesc_Injected
UnityEngine.XR.XRSettings::set_eyeTextureResolutionScale
UnityEngine.XR.XRSettings::get_stereoRenderingMode
UnityEngine.Yoga.Native::YGConfigGetUseWebDefaults
UnityEngine.Yoga.Native::YGNodeGetHasNewLayout
UnityEngine.Yoga.Native::YGNodeIsDirty
UnityEngine.Yoga.Native::YGConfigGetDefault
UnityEngine.Yoga.Native::YGConfigNew
UnityEngine.Yoga.Native::YGNodeNewWithConfig
UnityEngine.Yoga.Native::YGNodeLayoutGetBorder
UnityEngine.Yoga.Native::YGNodeLayoutGetBottom
UnityEngine.Yoga.Native::YGNodeLayoutGetHeight
UnityEngine.Yoga.Native::YGNodeLayoutGetLeft
UnityEngine.Yoga.Native::YGNodeLayoutGetMargin
UnityEngine.Yoga.Native::YGNodeLayoutGetPadding
UnityEngine.Yoga.Native::YGNodeLayoutGetRight
UnityEngine.Yoga.Native::YGNodeLayoutGetTop
UnityEngine.Yoga.Native::YGNodeLayoutGetWidth
UnityEngine.Yoga.Native::YGConfigFreeInternal
UnityEngine.Yoga.Native::YGConfigSetPointScaleFactor
UnityEngine.Yoga.Native::YGConfigSetUseWebDefaults
UnityEngine.Yoga.Native::YGNodeCalculateLayout
UnityEngine.Yoga.Native::YGNodeCopyStyle
UnityEngine.Yoga.Native::YGNodeFreeInternal
UnityEngine.Yoga.Native::YGNodeInsertChild
UnityEngine.Yoga.Native::YGNodeMarkDirty
UnityEngine.Yoga.Native::YGNodeRemoveChild
UnityEngine.Yoga.Native::YGNodeRemoveMeasureFunc
UnityEngine.Yoga.Native::YGNodeSetConfig
UnityEngine.Yoga.Native::YGNodeSetHasNewLayout
UnityEngine.Yoga.Native::YGNodeSetMeasureFunc
UnityEngine.Yoga.Native::YGNodeStyleSetAlignContent
UnityEngine.Yoga.Native::YGNodeStyleSetAlignItems
UnityEngine.Yoga.Native::YGNodeStyleSetAlignSelf
UnityEngine.Yoga.Native::YGNodeStyleSetBorder
UnityEngine.Yoga.Native::YGNodeStyleSetDisplay
UnityEngine.Yoga.Native::YGNodeStyleSetFlex
UnityEngine.Yoga.Native::YGNodeStyleSetFlexBasis
UnityEngine.Yoga.Native::YGNodeStyleSetFlexBasisAuto
UnityEngine.Yoga.Native::YGNodeStyleSetFlexBasisPercent
UnityEngine.Yoga.Native::YGNodeStyleSetFlexDirection
UnityEngine.Yoga.Native::YGNodeStyleSetFlexGrow
UnityEngine.Yoga.Native::YGNodeStyleSetFlexShrink
UnityEngine.Yoga.Native::YGNodeStyleSetFlexWrap
UnityEngine.Yoga.Native::YGNodeStyleSetHeight
UnityEngine.Yoga.Native::YGNodeStyleSetHeightAuto
UnityEngine.Yoga.Native::YGNodeStyleSetHeightPercent
UnityEngine.Yoga.Native::YGNodeStyleSetJustifyContent
UnityEngine.Yoga.Native::YGNodeStyleSetMargin
UnityEngine.Yoga.Native::YGNodeStyleSetMarginAuto
UnityEngine.Yoga.Native::YGNodeStyleSetMarginPercent
UnityEngine.Yoga.Native::YGNodeStyleSetMaxHeight
UnityEngine.Yoga.Native::YGNodeStyleSetMaxHeightPercent
UnityEngine.Yoga.Native::YGNodeStyleSetMaxWidth
UnityEngine.Yoga.Native::YGNodeStyleSetMaxWidthPercent
UnityEngine.Yoga.Native::YGNodeStyleSetMinHeight
UnityEngine.Yoga.Native::YGNodeStyleSetMinHeightPercent
UnityEngine.Yoga.Native::YGNodeStyleSetMinWidth
UnityEngine.Yoga.Native::YGNodeStyleSetMinWidthPercent
UnityEngine.Yoga.Native::YGNodeStyleSetOverflow
UnityEngine.Yoga.Native::YGNodeStyleSetPadding
UnityEngine.Yoga.Native::YGNodeStyleSetPaddingPercent
UnityEngine.Yoga.Native::YGNodeStyleSetPosition
UnityEngine.Yoga.Native::YGNodeStyleSetPositionPercent
UnityEngine.Yoga.Native::YGNodeStyleSetPositionType
UnityEngine.Yoga.Native::YGNodeStyleSetWidth
UnityEngine.Yoga.Native::YGNodeStyleSetWidthAuto
UnityEngine.Yoga.Native::YGNodeStyleSetWidthPercent
UnityEngine.Yoga.Native::YGSetManagedObject
UnityEngine.Yoga.Native::YGNodeStyleGetDirection
UnityEngineInternal.Input.NativeInputSystem::get_currentTime
UnityEngineInternal.Input.NativeInputSystem::get_currentTimeOffsetToRealtimeSinceStartup
UnityEngineInternal.Input.NativeInputSystem::AllocateDeviceId
UnityEngineInternal.Input.NativeInputSystem::IOCTL
UnityEngineInternal.Input.NativeInputSystem::QueueInputEvent
UnityEngineInternal.Input.NativeInputSystem::SetPollingFrequency
UnityEngineInternal.Input.NativeInputSystem::Update
UnityEngineInternal.Input.NativeInputSystem::set_hasDeviceDiscoveredCallback
