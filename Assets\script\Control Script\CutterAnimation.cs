using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CutterAnimation : MonoBehaviour
{
    public GameObject Leftside, Rightside;
    public float animationDuration = 3f;
    private bool isAnimating = false;

    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.tag == "finishline" && !isAnimating)
        {
            StartCoroutine(SmoothRotateAnimation());
        }
    }

    IEnumerator SmoothRotateAnimation()
    {
        isAnimating = true;

        // Store initial rotations
        Quaternion leftInitialRotation = Leftside.transform.rotation;
        Quaternion rightInitialRotation = Rightside.transform.rotation;

        // Target rotations
        Quaternion leftTargetRotation = Quaternion.Euler(0f, -180f, 1f);
        Quaternion rightTargetRotation = Quaternion.Euler(0, 180, -1);

        float elapsedTime = 0f;

        while (elapsedTime < animationDuration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / animationDuration;

            // Smooth interpolation using Lerp
            Leftside.transform.rotation = Quaternion.Lerp(leftInitialRotation, leftTargetRotation, t);
            Rightside.transform.rotation = Quaternion.Lerp(rightInitialRotation, rightTargetRotation, t);

            yield return null;
        }

        // Ensure final rotation is exact
        Leftside.transform.rotation = leftTargetRotation;
        Rightside.transform.rotation = rightTargetRotation;

        isAnimating = false;
    }
}
