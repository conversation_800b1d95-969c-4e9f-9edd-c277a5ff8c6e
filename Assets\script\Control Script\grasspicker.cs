using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class grasspicker : MonoBehaviour
{
    public GameObject SpawnGrassBundle;
    public Slider progressSlider;
    public Text progressText;
    public GameObject completePanel;
     public GameObject canvas, tractorcam, machinecam,field;
    private int triggerCount = 0;
    private int triggersNeeded = 15;
    private static int bundleCount = 0;
    private int bundlesNeeded = 12;
    
    void Start()
    {
        UpdateUI();
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.tag == "CutGrass")
        {
            other.gameObject.SetActive(false);
            triggerCount++;
            
            // Check if we've reached 5 triggers
            if (triggerCount >= triggersNeeded)
            {
                // Spawn the grass bundle at this position
                if (SpawnGrassBundle != null)
                {
                    Instantiate(SpawnGrassBundle, transform.position, transform.rotation);
                    bundleCount++;
                    UpdateUI();
                    
                    // Check if we've completed 12 bundles
                    if (bundleCount >= bundlesNeeded)
                    {
                        CompleteTask();
                    }
                }
                
                // Reset the counter to repeat the process
                triggerCount = 0;
            }
        }
    }
    
    void UpdateUI()
    {
        if (progressSlider != null)
        {
            float progress = (float)bundleCount / bundlesNeeded;
            progressSlider.value = progress;
        }
        
        if (progressText != null)
        {
            float percentage = ((float)bundleCount / bundlesNeeded) * 100f;
            progressText.text = percentage.ToString("F0") + "%";
        }
    }
    
    void CompleteTask()
    {
        // Set slider to 100%
        if (progressSlider != null)
        {
            progressSlider.value = 1f;
        }
        
        // Set text to 100%
        if (progressText != null)
        {
            progressText.text = "100%";
        }
        
        Debug.Log("Task Completed! 12 bundles collected.");
        
        // Start completion sequence
        StartCoroutine(CompletionSequence());
    }
    
    IEnumerator CompletionSequence()
    {
        // Set machine cam to false and tractor cam to true
        if (machinecam != null)
        {
            machinecam.SetActive(false);
            canvas.SetActive(false);
            field.SetActive(false);
        }
        
        if(tractorcam != null)
        {
            tractorcam.SetActive(true);
        }
        
        // Wait for 10 seconds
        yield return new WaitForSeconds(10f);
        
        // Show completion panel
        if(completePanel != null)
        {
            completePanel.SetActive(true);
        }
    }
}
