using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Particlecontrol : MonoBehaviour
{
    public ParticleSystem[] Particle;
    void Start()
    {
        foreach (var p in Particle)
        {
            p.Stop();
        }
    }
    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.tag == "GrassField")
        {
        foreach (var p in Particle)
        {
            p.Play();
        }
        }

    }
    void OnTriggerExit(Collider other)
    {
        if (other.gameObject.tag == "GrassField")
        {
            foreach (var p in Particle)
        {
            p.Stop();
        }
        }

    }
}
