apply plugin: 'com.android.library'


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
// Android Resolver Dependencies Start
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4' // Assets/GoogleMobileAds/Editor/GoogleMobileAdsDependencies.xml:12
    implementation 'androidx.fragment:fragment:1.6.0' // Assets/#_AdManager_#/_ADS/Editor/Resources/GoogleMobileAdsDependencies.xml:21
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.6.1' // Assets/#_AdManager_#/_ADS/Editor/Resources/GoogleMobileAdsDependencies.xml:7
    implementation 'androidx.lifecycle:lifecycle-process:2.6.1' // Assets/#_AdManager_#/_ADS/Editor/Resources/GoogleMobileAdsDependencies.xml:13
    implementation 'com.google.android.gms:play-services-ads:24.2.0' // Assets/GoogleMobileAds/Editor/GoogleMobileAdsDependencies.xml:7
    implementation 'com.google.android.gms:play-services-base:18.2.0' // Assets/Firebase/Editor/AppDependencies.xml:17
    implementation 'com.google.android.ump:user-messaging-platform:3.2.0' // Assets/GoogleMobileAds/Editor/GoogleUmpDependencies.xml:7
    implementation 'com.google.firebase:firebase-analytics:21.3.0' // Assets/Firebase/Editor/AppDependencies.xml:15
    implementation 'com.google.firebase:firebase-analytics-unity:11.6.0' // Assets/Firebase/Editor/AnalyticsDependencies.xml:18
    implementation 'com.google.firebase:firebase-app-unity:11.6.0' // Assets/Firebase/Editor/AppDependencies.xml:22
    implementation 'com.google.firebase:firebase-common:20.3.3' // Assets/Firebase/Editor/AppDependencies.xml:13
// Android Resolver Dependencies End
    implementation(name: 'googlemobileads-unity', ext:'aar')
    implementation project('GoogleMobileAdsPlugin.androidlib')
    implementation project('FirebaseApp.androidlib')

}

// Android Resolver Exclusions Start
android {
  packagingOptions {
      exclude ('/lib/armeabi/*' + '*')
      exclude ('/lib/mips/*' + '*')
      exclude ('/lib/mips64/*' + '*')
      exclude ('/lib/x86/*' + '*')
      exclude ('/lib/x86_64/*' + '*')
  }
}
// Android Resolver Exclusions End
android {
    namespace "com.unity3d.player"
    ndkPath "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK"
    compileSdkVersion 36
    buildToolsVersion '34.0.0'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 36
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
        versionCode 24
        versionName '2.4'
        consumerProguardFiles 'proguard-unity.txt', 'proguard-user.txt'
    }

    lintOptions {
        abortOnError false
    }

    aaptOptions {
        noCompress = ['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

    packagingOptions {
        doNotStrip '*/armeabi-v7a/*.so'
        doNotStrip '*/arm64-v8a/*.so'
        jniLibs {
            useLegacyPackaging true
        }
    }
}






apply from: 'GoogleMobileAdsPlugin.androidlib\\packaging_options.gradle'